// Enhanced Order Tracking Service for PasaBuy Pal
import { prisma } from '@/lib/db'
// Define types since they don't exist in the current Prisma schema
type OrderStatusType = 'PENDING' | 'BOUGHT' | 'PACKED' | 'DELIVERED' | 'CANCELLED'
type OrderEventType = 'STATUS_CHANGE' | 'NOTE_ADDED' | 'PAYMENT_RECEIVED' | 'SHIPMENT_CREATED' | 'DELIVERY_ATTEMPTED' | 'INVOICED'

export interface OrderStatusChange {
  orderId: number
  fromStatus?: string
  toStatus: string
  statusType: OrderStatusType
  changedBy?: string
  changeReason?: string
  metadata?: Record<string, any>
}

export interface OrderEvent {
  orderId: number
  eventType: OrderEventType
  eventData?: Record<string, any>
  performedBy?: string
  notes?: string
}

export interface OrderMetricsData {
  orderId: number
  timeToFirstPurchase?: number
  timeToPacking?: number
  timeToInvoicing?: number
  timeToDelivery?: number
  totalProcessingTime?: number
  statusChangeCount?: number
  purchaseAttempts?: number
  packingAttempts?: number
  isRushed?: boolean
  isDelayed?: boolean
  hasIssues?: boolean
  efficiencyScore?: number
}

export class OrderTrackingService {
  /**
   * Record a status change in the order history
   */
  static async recordStatusChange(change: OrderStatusChange): Promise<void> {
    await prisma.orderStatusHistory.create({
      data: {
        orderId: change.orderId,
        fromStatus: change.fromStatus,
        toStatus: change.toStatus,
        statusType: change.statusType,
        changedBy: change.changedBy,
        changeReason: change.changeReason,
        metadata: change.metadata ? JSON.stringify(change.metadata) : null,
      }
    })
  }

  /**
   * Record an event in the order timeline
   */
  static async recordEvent(event: OrderEvent): Promise<void> {
    // Calculate duration from previous event if applicable
    const lastEvent = await prisma.orderTimeline.findFirst({
      where: { orderId: event.orderId },
      orderBy: { createdAt: 'desc' }
    })

    let duration: number | undefined
    if (lastEvent) {
      const timeDiff = Date.now() - lastEvent.createdAt.getTime()
      duration = Math.floor(timeDiff / (1000 * 60)) // Convert to minutes
    }

    await prisma.orderTimeline.create({
      data: {
        orderId: event.orderId,
        eventType: event.eventType,
        eventData: event.eventData ? JSON.stringify(event.eventData) : null,
        duration,
        performedBy: event.performedBy,
        notes: event.notes,
      }
    })
  }

  /**
   * Update or create order metrics
   */
  static async updateMetrics(metrics: OrderMetricsData): Promise<void> {
    await prisma.orderMetrics.upsert({
      where: { orderId: metrics.orderId },
      update: {
        ...metrics,
        lastCalculated: new Date(),
      },
      create: {
        ...metrics,
        lastCalculated: new Date(),
      }
    })
  }

  /**
   * Calculate and update metrics for an order
   */
  static async calculateOrderMetrics(orderId: number): Promise<void> {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        timeline: {
          orderBy: { createdAt: 'asc' }
        },
        statusHistory: {
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!order) return

    const metrics: Partial<OrderMetricsData> = {
      orderId,
      statusChangeCount: order.statusHistory.length,
    }

    // Calculate timing metrics from timeline events
    const events = order.timeline
    const createdEvent = events.find(e => e.eventType === 'CREATED')
    const purchasedEvent = events.find(e => e.eventType === 'PURCHASED')
    const packedEvent = events.find(e => e.eventType === 'PACKED')
    const invoicedEvent = events.find(e => e.eventType === 'INVOICED')
    const deliveredEvent = events.find(e => e.eventType === 'DELIVERED')

    if (createdEvent && purchasedEvent) {
      metrics.timeToFirstPurchase = Math.floor(
        (purchasedEvent.createdAt.getTime() - createdEvent.createdAt.getTime()) / (1000 * 60)
      )
    }

    if (purchasedEvent && packedEvent) {
      metrics.timeToPacking = Math.floor(
        (packedEvent.createdAt.getTime() - purchasedEvent.createdAt.getTime()) / (1000 * 60)
      )
    }

    if (packedEvent && invoicedEvent) {
      metrics.timeToInvoicing = Math.floor(
        (invoicedEvent.createdAt.getTime() - packedEvent.createdAt.getTime()) / (1000 * 60)
      )
    }

    if (invoicedEvent && deliveredEvent) {
      metrics.timeToDelivery = Math.floor(
        (deliveredEvent.createdAt.getTime() - invoicedEvent.createdAt.getTime()) / (1000 * 60)
      )
    }

    if (createdEvent && deliveredEvent) {
      metrics.totalProcessingTime = Math.floor(
        (deliveredEvent.createdAt.getTime() - createdEvent.createdAt.getTime()) / (1000 * 60)
      )
    }

    // Calculate attempt counts
    metrics.purchaseAttempts = events.filter(e => e.eventType === 'PURCHASED').length
    metrics.packingAttempts = events.filter(e => e.eventType === 'PACKED').length

    // Detect issues (status reversals)
    const hasReversals = events.some(e => 
      e.eventType === 'PURCHASE_CANCELLED' || e.eventType === 'PACKING_CANCELLED'
    )
    metrics.hasIssues = hasReversals

    // Calculate efficiency score (0-100 based on processing time vs average)
    if (metrics.totalProcessingTime) {
      // This would ideally compare against historical averages
      // For now, using a simple heuristic: faster = higher score
      const maxReasonableTime = 7 * 24 * 60 // 7 days in minutes
      metrics.efficiencyScore = Math.max(0, 
        100 - (metrics.totalProcessingTime / maxReasonableTime) * 100
      )
      
      // Mark as rushed if completed in less than 1 day
      metrics.isRushed = metrics.totalProcessingTime < 24 * 60
      
      // Mark as delayed if took more than 3 days
      metrics.isDelayed = metrics.totalProcessingTime > 3 * 24 * 60
    }

    await this.updateMetrics(metrics as OrderMetricsData)
  }

  /**
   * Handle order status update with full tracking
   */
  static async handleOrderUpdate(
    orderId: number,
    updates: Record<string, any>,
    context: {
      changedBy?: string
      changeReason?: string
      isBulkOperation?: boolean
      bulkOperationId?: string
    } = {}
  ): Promise<void> {
    const order = await prisma.order.findUnique({ where: { id: orderId } })
    if (!order) return

    const promises: Promise<any>[] = []

    // Track isBought changes
    if ('isBought' in updates && updates.isBought !== order.isBought) {
      const statusChange: OrderStatusChange = {
        orderId,
        fromStatus: order.isBought ? 'bought' : 'pending',
        toStatus: updates.isBought ? 'bought' : 'pending',
        statusType: 'PURCHASE_STATUS',
        changedBy: context.changedBy,
        changeReason: context.changeReason,
        metadata: context.isBulkOperation ? { 
          bulkOperationId: context.bulkOperationId 
        } : undefined
      }
      promises.push(this.recordStatusChange(statusChange))

      const eventType = updates.isBought ? 'PURCHASED' : 'PURCHASE_CANCELLED'
      const event: OrderEvent = {
        orderId,
        eventType,
        performedBy: context.changedBy,
        notes: context.changeReason,
        eventData: context.isBulkOperation ? { 
          bulkOperationId: context.bulkOperationId 
        } : undefined
      }
      promises.push(this.recordEvent(event))
    }

    // Track packingStatus changes
    if ('packingStatus' in updates && updates.packingStatus !== order.packingStatus) {
      const statusChange: OrderStatusChange = {
        orderId,
        fromStatus: order.packingStatus,
        toStatus: updates.packingStatus,
        statusType: 'PACKING_STATUS',
        changedBy: context.changedBy,
        changeReason: context.changeReason,
        metadata: context.isBulkOperation ? { 
          bulkOperationId: context.bulkOperationId 
        } : undefined
      }
      promises.push(this.recordStatusChange(statusChange))

      let eventType: OrderEventType = 'SYSTEM_UPDATE'
      if (updates.packingStatus === 'Packed') {
        eventType = 'PACKED'
      } else if (order.packingStatus === 'Packed') {
        eventType = 'PACKING_CANCELLED'
      }

      const event: OrderEvent = {
        orderId,
        eventType,
        performedBy: context.changedBy,
        notes: context.changeReason,
        eventData: context.isBulkOperation ? { 
          bulkOperationId: context.bulkOperationId 
        } : undefined
      }
      promises.push(this.recordEvent(event))
    }

    // Track delivery status changes
    if ('deliveryStatus' in updates && updates.deliveryStatus !== order.deliveryStatus) {
      const statusChange: OrderStatusChange = {
        orderId,
        fromStatus: order.deliveryStatus || 'Not Delivered',
        toStatus: updates.deliveryStatus,
        statusType: 'DELIVERY_STATUS',
        changedBy: context.changedBy,
        changeReason: context.changeReason,
        metadata: {
          deliveryMethod: updates.deliveryMethod,
          trackingNumber: updates.trackingNumber,
          ...(context.isBulkOperation ? { bulkOperationId: context.bulkOperationId } : {})
        }
      }
      promises.push(this.recordStatusChange(statusChange))

      let eventType: OrderEventType = 'SYSTEM_UPDATE'
      if (updates.deliveryStatus === 'Delivered') {
        eventType = 'DELIVERED'
      } else if (updates.deliveryStatus === 'Confirmed') {
        eventType = 'DELIVERY_CONFIRMED'
      }

      const event: OrderEvent = {
        orderId,
        eventType,
        performedBy: context.changedBy,
        notes: context.changeReason,
        eventData: {
          deliveryMethod: updates.deliveryMethod,
          trackingNumber: updates.trackingNumber,
          deliveryDate: updates.deliveryDate,
          ...(context.isBulkOperation ? { bulkOperationId: context.bulkOperationId } : {})
        }
      }
      promises.push(this.recordEvent(event))
    }

    // Execute all tracking operations
    await Promise.all(promises)

    // Recalculate metrics after changes
    await this.calculateOrderMetrics(orderId)
  }

  /**
   * Get order tracking summary
   */
  static async getOrderTrackingSummary(orderId: number) {
    return await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        statusHistory: {
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        timeline: {
          orderBy: { createdAt: 'desc' },
          take: 20
        },
        metrics: true,
        storeCode: true,
        customer: true
      }
    })
  }
}
