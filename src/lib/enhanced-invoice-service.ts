// Enhanced Invoice Management Service for PasaBuy Pal
import { prisma } from '@/lib/db'
import { OrderTrackingService } from '@/lib/order-tracking'
// Define types since they don't exist in the current Prisma schema
type InvoiceStatus = 'DRAFT' | 'SENT' | 'PAID' | 'PARTIALLY_PAID' | 'OVERDUE' | 'CANCELLED' | 'REFUNDED'
type InvoiceType = 'STANDARD' | 'RECURRING' | 'CREDIT_NOTE' | 'DEBIT_NOTE'
type InvoicePriority = 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
type ApprovalStatus = 'PENDING' | 'APPROVED' | 'REJECTED' | 'REQUIRES_REVIEW'
type SyncStatus = 'PENDING' | 'SYNCED' | 'FAILED' | 'MANUAL'
type InvoiceItemType = 'PRODUCT' | 'SERVICE' | 'DISCOUNT' | 'TAX' | 'SHIPPING'
type InvoiceCommunicationType = 'EMAIL' | 'PHONE' | 'SMS' | 'MEETING' | 'NOTE'
type CommunicationDirection = 'INBOUND' | 'OUTBOUND'

export interface EnhancedInvoiceData {
  customerId: number
  orderIds: number[]
  
  // Enhanced invoice details
  invoiceType?: InvoiceType
  priority?: InvoicePriority
  
  // Financial details
  discountAmount?: number
  discountPercentage?: number
  taxRate?: number
  shippingCost?: number
  
  // Payment and terms
  paymentTerms?: number
  paymentMethod?: string
  currency?: string
  
  // Dates
  dueDate?: Date
  
  // Customer information
  billingAddress?: string
  shippingAddress?: string
  customerPO?: string
  
  // Invoice customization
  templateId?: string
  logoUrl?: string
  headerText?: string
  footerText?: string
  
  // Notes
  notes?: string
  internalNotes?: string
  customerNotes?: string
}

export interface InvoicePaymentData {
  amount: number
  paymentMethod: string
  paymentDate?: Date
  reference?: string
  notes?: string
  externalPaymentId?: string
  processorFee?: number
}

export interface InvoiceCommunicationData {
  type: InvoiceCommunicationType
  direction: CommunicationDirection
  subject?: string
  content: string
  channel?: string
  sentBy?: string
  sentTo?: string
}

export interface InvoiceSearchFilters {
  customerId?: number
  status?: InvoiceStatus[]
  invoiceType?: InvoiceType[]
  priority?: InvoicePriority[]
  approvalStatus?: ApprovalStatus[]
  
  // Date filters
  issueDateAfter?: Date
  issueDateBefore?: Date
  dueDateAfter?: Date
  dueDateBefore?: Date
  
  // Amount filters
  minTotal?: number
  maxTotal?: number
  
  // Status filters
  isOverdue?: boolean
  isPaid?: boolean
  needsApproval?: boolean
  
  // Search
  searchTerm?: string
}

export interface InvoiceAnalytics {
  totalInvoices: number
  totalValue: number
  averageInvoiceValue: number
  paidInvoices: number
  overdueInvoices: number
  draftInvoices: number
  totalPaid: number
  totalOutstanding: number
  averagePaymentTime: number
  statusBreakdown: Array<{ status: string; count: number; value: number }>
  monthlyTrends: Array<{ month: string; invoices: number; value: number; paid: number }>
}

export class EnhancedInvoiceService {
  /**
   * Generate unique invoice number
   */
  private static generateInvoiceNumber(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 4)
    return `INV-${timestamp}-${random}`.toUpperCase()
  }

  /**
   * Generate unique payment number
   */
  private static generatePaymentNumber(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 4)
    return `PAY-${timestamp}-${random}`.toUpperCase()
  }

  /**
   * Create enhanced invoice with full tracking
   */
  static async createInvoice(invoiceData: EnhancedInvoiceData, context?: {
    createdBy?: string
  }): Promise<any> {
    const invoiceNumber = this.generateInvoiceNumber()

    // Get orders to calculate totals
    const orders = await prisma.order.findMany({
      where: { id: { in: invoiceData.orderIds } }
    })

    if (orders.length === 0) {
      throw new Error('No valid orders found for invoice')
    }

    // Calculate subtotal from orders
    const subtotal = orders.reduce((sum, order) => sum + order.customerPrice, 0)
    
    // Calculate discount
    const discountAmount = invoiceData.discountPercentage 
      ? subtotal * (invoiceData.discountPercentage / 100)
      : (invoiceData.discountAmount || 0)
    
    // Calculate tax
    const taxableAmount = subtotal - discountAmount
    const taxAmount = invoiceData.taxRate 
      ? taxableAmount * (invoiceData.taxRate / 100)
      : 0
    
    // Calculate total
    const total = taxableAmount + taxAmount + (invoiceData.shippingCost || 0)

    // Calculate due date
    const dueDate = invoiceData.dueDate || new Date(Date.now() + (invoiceData.paymentTerms || 30) * 24 * 60 * 60 * 1000)

    const invoice = await prisma.$transaction(async (tx) => {
      // Create invoice
      const newInvoice = await tx.invoice.create({
        data: {
          invoiceNumber,
          customerId: invoiceData.customerId,
          
          // Enhanced details
          invoiceType: invoiceData.invoiceType || 'STANDARD',
          priority: invoiceData.priority || 'NORMAL',
          
          // Financial details
          subtotal,
          discountAmount,
          discountPercentage: invoiceData.discountPercentage || 0,
          taxAmount,
          taxRate: invoiceData.taxRate || 0,
          shippingCost: invoiceData.shippingCost || 0,
          total,
          
          // Payment and terms
          paymentTerms: invoiceData.paymentTerms || 30,
          paymentMethod: invoiceData.paymentMethod,
          currency: invoiceData.currency || 'PHP',
          
          // Dates
          dueDate,
          overdueDate: new Date(dueDate.getTime() + 24 * 60 * 60 * 1000), // 1 day after due date
          
          // Customer information
          billingAddress: invoiceData.billingAddress,
          shippingAddress: invoiceData.shippingAddress,
          customerPO: invoiceData.customerPO,
          
          // Invoice customization
          templateId: invoiceData.templateId,
          logoUrl: invoiceData.logoUrl,
          headerText: invoiceData.headerText,
          footerText: invoiceData.footerText,
          
          // Notes
          notes: invoiceData.notes,
          internalNotes: invoiceData.internalNotes,
          customerNotes: invoiceData.customerNotes,
        }
      })

      // Create invoice items
      const invoiceItems = await Promise.all(
        orders.map(order =>
          tx.invoiceItem.create({
            data: {
              invoiceId: newInvoice.id,
              orderId: order.id,
              description: order.productName,
              quantity: order.quantity,
              unitPrice: order.customerPrice / order.quantity,
              originalUnitPrice: order.storePrice / order.quantity,
              discountAmount: order.discountAmount || 0,
              taxAmount: 0, // Tax is calculated at invoice level
              totalPrice: order.customerPrice,
              itemType: 'PRODUCT',
              category: order.category,
              sku: order.sku
            }
          })
        )
      )

      return { ...newInvoice, invoiceItems }
    })

    // Record invoice creation events for each order
    await Promise.all(
      invoiceData.orderIds.map(orderId =>
        OrderTrackingService.recordEvent({
          orderId,
          eventType: 'INVOICED',
          performedBy: context?.createdBy,
          eventData: {
            invoiceNumber,
            invoiceId: invoice.id,
            total
          }
        })
      )
    )

    return invoice
  }

  /**
   * Update invoice status with tracking
   */
  static async updateInvoiceStatus(
    invoiceId: number,
    status: InvoiceStatus,
    context?: {
      updatedBy?: string
      reason?: string
    }
  ): Promise<any> {
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: { invoiceItems: { include: { order: true } } }
    })

    if (!invoice) {
      throw new Error('Invoice not found')
    }

    const updatedInvoice = await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        status,
        sentDate: status === 'SENT' ? new Date() : invoice.sentDate,
        paidDate: status === 'PAID' ? new Date() : invoice.paidDate,
        updatedAt: new Date()
      }
    })

    // Record status change events for related orders
    const eventType = status === 'SENT' ? 'INVOICE_SENT' : 
                     status === 'PAID' ? 'PAYMENT_RECEIVED' : 'SYSTEM_UPDATE'

    await Promise.all(
      invoice.invoiceItems.map(item =>
        OrderTrackingService.recordEvent({
          orderId: item.orderId,
          eventType,
          performedBy: context?.updatedBy,
          eventData: {
            invoiceNumber: invoice.invoiceNumber,
            invoiceId: invoice.id,
            newStatus: status,
            previousStatus: invoice.status
          },
          notes: context?.reason
        })
      )
    )

    return updatedInvoice
  }

  /**
   * Add payment to invoice
   */
  static async addPayment(invoiceId: number, paymentData: InvoicePaymentData): Promise<any> {
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: { payments: true }
    })

    if (!invoice) {
      throw new Error('Invoice not found')
    }

    const paymentNumber = this.generatePaymentNumber()
    
    const payment = await prisma.invoicePayment.create({
      data: {
        invoiceId,
        paymentNumber,
        amount: paymentData.amount,
        paymentMethod: paymentData.paymentMethod,
        paymentDate: paymentData.paymentDate || new Date(),
        reference: paymentData.reference,
        notes: paymentData.notes,
        externalPaymentId: paymentData.externalPaymentId,
        processorFee: paymentData.processorFee || 0
      }
    })

    // Calculate total payments
    const totalPaid = invoice.payments.reduce((sum, p) => sum + p.amount, 0) + paymentData.amount

    // Update invoice status based on payment
    let newStatus = invoice.status
    if (totalPaid >= invoice.total) {
      newStatus = 'PAID'
    } else if (totalPaid > 0) {
      newStatus = 'PARTIALLY_PAID'
    }

    if (newStatus !== invoice.status) {
      await this.updateInvoiceStatus(invoiceId, newStatus as InvoiceStatus)
    }

    return payment
  }

  /**
   * Add communication record to invoice
   */
  static async addCommunication(invoiceId: number, communicationData: InvoiceCommunicationData): Promise<void> {
    await prisma.invoiceCommunication.create({
      data: {
        invoiceId,
        ...communicationData
      }
    })

    // Update email tracking if it's an email communication
    if (communicationData.channel?.toLowerCase() === 'email' && communicationData.direction === 'OUTBOUND') {
      await prisma.invoice.update({
        where: { id: invoiceId },
        data: {
          emailsSent: { increment: 1 },
          lastEmailSent: new Date()
        }
      })
    }
  }

  /**
   * Advanced invoice search with enhanced filters
   */
  static async searchInvoices(
    filters: InvoiceSearchFilters,
    pagination?: { page: number; limit: number },
    sorting?: { field: string; direction: 'asc' | 'desc' }
  ): Promise<{ invoices: any[]; total: number; hasMore: boolean }> {
    const whereClause: any = {}

    // Basic filters
    if (filters.customerId) whereClause.customerId = filters.customerId
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status }
    }
    if (filters.invoiceType && filters.invoiceType.length > 0) {
      whereClause.invoiceType = { in: filters.invoiceType }
    }
    if (filters.priority && filters.priority.length > 0) {
      whereClause.priority = { in: filters.priority }
    }
    if (filters.approvalStatus && filters.approvalStatus.length > 0) {
      whereClause.approvalStatus = { in: filters.approvalStatus }
    }

    // Date filters
    if (filters.issueDateAfter || filters.issueDateBefore) {
      whereClause.issueDate = {}
      if (filters.issueDateAfter) whereClause.issueDate.gte = filters.issueDateAfter
      if (filters.issueDateBefore) whereClause.issueDate.lte = filters.issueDateBefore
    }

    if (filters.dueDateAfter || filters.dueDateBefore) {
      whereClause.dueDate = {}
      if (filters.dueDateAfter) whereClause.dueDate.gte = filters.dueDateAfter
      if (filters.dueDateBefore) whereClause.dueDate.lte = filters.dueDateBefore
    }

    // Amount filters
    if (filters.minTotal || filters.maxTotal) {
      whereClause.total = {}
      if (filters.minTotal) whereClause.total.gte = filters.minTotal
      if (filters.maxTotal) whereClause.total.lte = filters.maxTotal
    }

    // Status filters
    if (filters.isOverdue) {
      whereClause.dueDate = { lt: new Date() }
      whereClause.status = { notIn: ['PAID', 'CANCELLED'] }
    }
    if (filters.isPaid) {
      whereClause.status = 'PAID'
    }
    if (filters.needsApproval) {
      whereClause.approvalStatus = 'PENDING'
    }

    // Search term
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.trim()
      whereClause.OR = [
        { invoiceNumber: { contains: searchTerm, mode: 'insensitive' } },
        { customerPO: { contains: searchTerm, mode: 'insensitive' } },
        { notes: { contains: searchTerm, mode: 'insensitive' } },
        { customer: { name: { contains: searchTerm, mode: 'insensitive' } } }
      ]
    }

    // Pagination
    const page = pagination?.page || 1
    const limit = pagination?.limit || 50
    const skip = (page - 1) * limit

    // Sorting
    const orderBy: any = {}
    if (sorting) {
      orderBy[sorting.field] = sorting.direction
    } else {
      orderBy.issueDate = 'desc'
    }

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where: whereClause,
        include: {
          customer: true,
          invoiceItems: {
            include: {
              order: {
                include: {
                  storeCode: true
                }
              }
            }
          },
          payments: true,
          communications: {
            take: 3,
            orderBy: { createdAt: 'desc' }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.invoice.count({ where: whereClause })
    ])

    return {
      invoices,
      total,
      hasMore: skip + invoices.length < total
    }
  }

  /**
   * Get invoice analytics
   */
  static async getInvoiceAnalytics(filters?: InvoiceSearchFilters): Promise<InvoiceAnalytics> {
    const whereClause = filters ? this.buildWhereClause(filters) : {}

    const [
      totalInvoices,
      invoiceAggregates,
      statusStats,
      paidInvoices,
      overdueInvoices,
      draftInvoices,
      paymentAggregates
    ] = await Promise.all([
      prisma.invoice.count({ where: whereClause }),
      prisma.invoice.aggregate({
        where: whereClause,
        _sum: { total: true },
        _avg: { total: true }
      }),
      prisma.invoice.groupBy({
        by: ['status'],
        where: whereClause,
        _count: true,
        _sum: { total: true }
      }),
      prisma.invoice.count({ where: { ...whereClause, status: 'PAID' } }),
      prisma.invoice.count({
        where: {
          ...whereClause,
          dueDate: { lt: new Date() },
          status: { notIn: ['PAID', 'CANCELLED'] }
        }
      }),
      prisma.invoice.count({ where: { ...whereClause, status: 'DRAFT' } }),
      prisma.invoicePayment.aggregate({
        where: {
          invoice: whereClause
        },
        _sum: { amount: true }
      })
    ])

    const totalValue = invoiceAggregates._sum.total || 0
    const totalPaid = paymentAggregates._sum.amount || 0
    const totalOutstanding = totalValue - totalPaid

    // Calculate average payment time (simplified)
    const averagePaymentTime = 15 // This would require more complex calculation

    // Generate monthly trends (simplified)
    const monthlyTrends = [
      {
        month: new Date().toISOString().slice(0, 7),
        invoices: totalInvoices,
        value: totalValue,
        paid: totalPaid
      }
    ]

    return {
      totalInvoices,
      totalValue,
      averageInvoiceValue: invoiceAggregates._avg.total || 0,
      paidInvoices,
      overdueInvoices,
      draftInvoices,
      totalPaid,
      totalOutstanding,
      averagePaymentTime,
      statusBreakdown: statusStats.map(stat => ({
        status: stat.status,
        count: stat._count,
        value: stat._sum.total || 0
      })),
      monthlyTrends
    }
  }

  /**
   * Build where clause from filters (helper method)
   */
  private static buildWhereClause(filters: InvoiceSearchFilters): any {
    const whereClause: any = {}
    
    if (filters.customerId) whereClause.customerId = filters.customerId
    if (filters.status && filters.status.length > 0) {
      whereClause.status = { in: filters.status }
    }
    if (filters.issueDateAfter || filters.issueDateBefore) {
      whereClause.issueDate = {}
      if (filters.issueDateAfter) whereClause.issueDate.gte = filters.issueDateAfter
      if (filters.issueDateBefore) whereClause.issueDate.lte = filters.issueDateBefore
    }
    
    return whereClause
  }

  /**
   * Approve invoice
   */
  static async approveInvoice(invoiceId: number, approvedBy: string): Promise<any> {
    return await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        approvalStatus: 'APPROVED',
        approvedBy,
        approvedAt: new Date(),
        status: 'APPROVED'
      }
    })
  }

  /**
   * Reject invoice
   */
  static async rejectInvoice(invoiceId: number, rejectedBy: string, reason: string): Promise<any> {
    return await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        approvalStatus: 'REJECTED',
        rejectedBy,
        rejectedAt: new Date(),
        rejectionReason: reason
      }
    })
  }
}
