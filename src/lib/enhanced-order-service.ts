// Enhanced Order Management Service for PasaBuy Pal
import { prisma } from '@/lib/db'
import { OrderTrackingService } from '@/lib/order-tracking'
// Define types since they don't exist in the current Prisma schema
type OrderPriority = 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
type OrderUrgency = 'STANDARD' | 'RUSH' | 'EMERGENCY'
type OrderSource = 'WEBSITE' | 'PHONE' | 'EMAIL' | 'WALK_IN' | 'REFERRAL'
type DiscountType = 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'BULK'
type OrderNoteType = 'GENERAL' | 'DELIVERY' | 'PAYMENT' | 'CUSTOMER_REQUEST' | 'INTERNAL'

export interface EnhancedOrderData {
  // Basic order data
  productName: string
  quantity: number
  usageUnit?: string
  comment?: string
  imageFilename?: string
  storePrice: number
  pasabuyFee: number
  customerPrice?: number
  storeCodeId?: number
  customerId?: number
  
  // Enhanced order fields
  priority?: OrderPriority
  category?: string
  brand?: string
  model?: string
  sku?: string
  barcode?: string
  
  // Advanced pricing
  originalPrice?: number
  discountAmount?: number
  discountType?: DiscountType
  discountReason?: string
  taxAmount?: number
  taxRate?: number
  
  // Order relationships
  parentOrderId?: number
  orderGroupId?: string
  
  // Metadata
  source?: OrderSource
  urgency?: OrderUrgency
  specialInstructions?: string
  internalNotes?: string
  customerNotes?: string
  
  // Lifecycle
  estimatedDelivery?: Date
  requestedDelivery?: Date
  
  // Tags and notes
  tags?: string[]
  notes?: Array<{
    content: string
    noteType?: OrderNoteType
    isInternal?: boolean
    authorName?: string
  }>
}

export interface OrderSearchFilters {
  // Basic filters
  productName?: string
  customerId?: number
  storeCodeId?: number
  isBought?: boolean
  packingStatus?: string
  deliveryStatus?: string
  
  // Enhanced filters
  priority?: OrderPriority[]
  urgency?: OrderUrgency[]
  source?: OrderSource[]
  category?: string[]
  brand?: string[]
  tags?: string[]
  
  // Date filters
  createdAfter?: Date
  createdBefore?: Date
  estimatedDeliveryAfter?: Date
  estimatedDeliveryBefore?: Date
  
  // Price filters
  minPrice?: number
  maxPrice?: number
  
  // Status filters
  hasDiscount?: boolean
  isOverdue?: boolean
  hasIssues?: boolean
  
  // Search
  searchTerm?: string
}

export interface OrderBundleData {
  parentOrderData: EnhancedOrderData
  childOrders: EnhancedOrderData[]
  bundleDiscount?: number
  bundleDiscountType?: DiscountType
}

export class EnhancedOrderService {
  /**
   * Generate unique order number
   */
  private static generateOrderNumber(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substr(2, 5)
    return `ORD-${timestamp}-${random}`.toUpperCase()
  }

  /**
   * Create enhanced order with full tracking
   */
  static async createOrder(orderData: EnhancedOrderData, context?: {
    createdBy?: string
    source?: string
  }): Promise<any> {
    const orderNumber = this.generateOrderNumber()
    
    // Calculate customer price if not provided
    const calculatedCustomerPrice = orderData.customerPrice ?? 
      (orderData.storePrice + orderData.pasabuyFee - (orderData.discountAmount || 0))

    const order = await prisma.order.create({
      data: {
        // Basic fields
        productName: orderData.productName.trim(),
        quantity: orderData.quantity,
        usageUnit: orderData.usageUnit?.trim() || null,
        comment: orderData.comment?.trim() || null,
        imageFilename: orderData.imageFilename || null,
        storePrice: orderData.storePrice,
        pasabuyFee: orderData.pasabuyFee,
        customerPrice: calculatedCustomerPrice,
        storeCodeId: orderData.storeCodeId || null,
        customerId: orderData.customerId || null,
        
        // Enhanced fields
        orderNumber,
        priority: orderData.priority || 'NORMAL',
        category: orderData.category?.trim() || null,
        brand: orderData.brand?.trim() || null,
        model: orderData.model?.trim() || null,
        sku: orderData.sku?.trim() || null,
        barcode: orderData.barcode?.trim() || null,
        
        // Advanced pricing
        originalPrice: orderData.originalPrice || orderData.storePrice,
        discountAmount: orderData.discountAmount || 0,
        discountType: orderData.discountType || null,
        discountReason: orderData.discountReason?.trim() || null,
        taxAmount: orderData.taxAmount || 0,
        taxRate: orderData.taxRate || 0,
        
        // Order relationships
        parentOrderId: orderData.parentOrderId || null,
        orderGroupId: orderData.orderGroupId?.trim() || null,
        
        // Metadata
        source: orderData.source || 'MANUAL',
        urgency: orderData.urgency || 'NORMAL',
        specialInstructions: orderData.specialInstructions?.trim() || null,
        internalNotes: orderData.internalNotes?.trim() || null,
        customerNotes: orderData.customerNotes?.trim() || null,
        
        // Lifecycle
        estimatedDelivery: orderData.estimatedDelivery || null,
        requestedDelivery: orderData.requestedDelivery || null,
      },
      include: {
        storeCode: true,
        customer: true
      }
    })

    // Add tags if provided
    if (orderData.tags && orderData.tags.length > 0) {
      await this.addOrderTags(order.id, orderData.tags)
    }

    // Add notes if provided
    if (orderData.notes && orderData.notes.length > 0) {
      await Promise.all(
        orderData.notes.map(note =>
          this.addOrderNote(order.id, {
            content: note.content,
            noteType: note.noteType || 'GENERAL',
            isInternal: note.isInternal || false,
            authorName: note.authorName || context?.createdBy
          })
        )
      )
    }

    // Record order creation event
    await OrderTrackingService.recordEvent({
      orderId: order.id,
      eventType: 'CREATED',
      performedBy: context?.createdBy,
      eventData: {
        orderNumber,
        source: orderData.source || 'MANUAL',
        priority: orderData.priority || 'NORMAL',
        urgency: orderData.urgency || 'NORMAL'
      }
    })

    return order
  }

  /**
   * Create order bundle with parent-child relationships
   */
  static async createOrderBundle(bundleData: OrderBundleData, context?: {
    createdBy?: string
  }): Promise<any> {
    const orderGroupId = `BUNDLE-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`

    // Create parent order
    const parentOrder = await this.createOrder({
      ...bundleData.parentOrderData,
      orderGroupId,
      discountAmount: bundleData.bundleDiscount || bundleData.parentOrderData.discountAmount,
      discountType: bundleData.bundleDiscountType || bundleData.parentOrderData.discountType
    }, context)

    // Create child orders
    const childOrders = await Promise.all(
      bundleData.childOrders.map(childOrderData =>
        this.createOrder({
          ...childOrderData,
          parentOrderId: parentOrder.id,
          orderGroupId
        }, context)
      )
    )

    return {
      parentOrder,
      childOrders,
      orderGroupId
    }
  }

  /**
   * Advanced order search with enhanced filters
   */
  static async searchOrders(
    filters: OrderSearchFilters,
    pagination?: { page: number; limit: number },
    sorting?: { field: string; direction: 'asc' | 'desc' }
  ): Promise<{ orders: any[]; total: number; hasMore: boolean }> {
    const whereClause: any = {}

    // Basic filters
    if (filters.productName) {
      whereClause.productName = { contains: filters.productName, mode: 'insensitive' }
    }
    if (filters.customerId) whereClause.customerId = filters.customerId
    if (filters.storeCodeId) whereClause.storeCodeId = filters.storeCodeId
    if (filters.isBought !== undefined) whereClause.isBought = filters.isBought
    if (filters.packingStatus) whereClause.packingStatus = filters.packingStatus
    if (filters.deliveryStatus) whereClause.deliveryStatus = filters.deliveryStatus

    // Enhanced filters
    if (filters.priority && filters.priority.length > 0) {
      whereClause.priority = { in: filters.priority }
    }
    if (filters.urgency && filters.urgency.length > 0) {
      whereClause.urgency = { in: filters.urgency }
    }
    if (filters.source && filters.source.length > 0) {
      whereClause.source = { in: filters.source }
    }
    if (filters.category && filters.category.length > 0) {
      whereClause.category = { in: filters.category }
    }
    if (filters.brand && filters.brand.length > 0) {
      whereClause.brand = { in: filters.brand }
    }

    // Date filters
    if (filters.createdAfter || filters.createdBefore) {
      whereClause.createdAt = {}
      if (filters.createdAfter) whereClause.createdAt.gte = filters.createdAfter
      if (filters.createdBefore) whereClause.createdAt.lte = filters.createdBefore
    }

    // Price filters
    if (filters.minPrice || filters.maxPrice) {
      whereClause.customerPrice = {}
      if (filters.minPrice) whereClause.customerPrice.gte = filters.minPrice
      if (filters.maxPrice) whereClause.customerPrice.lte = filters.maxPrice
    }

    // Status filters
    if (filters.hasDiscount) {
      whereClause.discountAmount = { gt: 0 }
    }
    if (filters.hasIssues) {
      whereClause.metrics = { hasIssues: true }
    }

    // Tag filters
    if (filters.tags && filters.tags.length > 0) {
      whereClause.tags = {
        some: {
          tag: { in: filters.tags }
        }
      }
    }

    // Search term (searches across multiple fields)
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.trim()
      whereClause.OR = [
        { productName: { contains: searchTerm, mode: 'insensitive' } },
        { orderNumber: { contains: searchTerm, mode: 'insensitive' } },
        { sku: { contains: searchTerm, mode: 'insensitive' } },
        { barcode: { contains: searchTerm, mode: 'insensitive' } },
        { brand: { contains: searchTerm, mode: 'insensitive' } },
        { category: { contains: searchTerm, mode: 'insensitive' } },
        { comment: { contains: searchTerm, mode: 'insensitive' } }
      ]
    }

    // Pagination
    const page = pagination?.page || 1
    const limit = pagination?.limit || 50
    const skip = (page - 1) * limit

    // Sorting
    const orderBy: any = {}
    if (sorting) {
      orderBy[sorting.field] = sorting.direction
    } else {
      orderBy.createdAt = 'desc'
    }

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where: whereClause,
        include: {
          storeCode: true,
          customer: true,
          tags: true,
          metrics: true,
          parentOrder: true,
          childOrders: true
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.order.count({ where: whereClause })
    ])

    return {
      orders,
      total,
      hasMore: skip + orders.length < total
    }
  }

  /**
   * Add tags to an order
   */
  static async addOrderTags(orderId: number, tags: string[]): Promise<void> {
    const tagData = tags.map(tag => ({
      orderId,
      tag: tag.trim(),
      color: this.generateTagColor(tag)
    }))

    // Create tags individually to handle duplicates
    for (const tag of tagData) {
      try {
        await prisma.orderTag.create({
          data: tag
        })
      } catch (error) {
        // Ignore duplicate errors
        console.log('Tag already exists, skipping:', tag)
      }
    }
  }

  /**
   * Add note to an order
   */
  static async addOrderNote(orderId: number, noteData: {
    content: string
    noteType?: OrderNoteType
    isInternal?: boolean
    authorName?: string
  }): Promise<void> {
    await prisma.orderNote.create({
      data: {
        orderId,
        content: noteData.content,
        noteType: noteData.noteType || 'GENERAL',
        isInternal: noteData.isInternal || false,
        authorName: noteData.authorName
      }
    })
  }

  /**
   * Generate color for tag based on tag name
   */
  private static generateTagColor(tag: string): string {
    const colors = [
      '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
      '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
      '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
      '#ec4899', '#f43f5e'
    ]
    
    let hash = 0
    for (let i = 0; i < tag.length; i++) {
      hash = tag.charCodeAt(i) + ((hash << 5) - hash)
    }
    
    return colors[Math.abs(hash) % colors.length]
  }

  /**
   * Get order analytics and insights
   */
  static async getOrderAnalytics(filters?: OrderSearchFilters): Promise<{
    totalOrders: number
    totalValue: number
    averageOrderValue: number
    categoryBreakdown: Array<{ category: string; count: number; value: number }>
    brandBreakdown: Array<{ brand: string; count: number; value: number }>
    priorityBreakdown: Array<{ priority: string; count: number }>
    sourceBreakdown: Array<{ source: string; count: number }>
  }> {
    const whereClause = filters ? this.buildWhereClause(filters) : {}

    const [
      totalOrders,
      orderAggregates,
      categoryStats,
      brandStats,
      priorityStats,
      sourceStats
    ] = await Promise.all([
      prisma.order.count({ where: whereClause }),
      prisma.order.aggregate({
        where: whereClause,
        _sum: { customerPrice: true },
        _avg: { customerPrice: true }
      }),
      prisma.order.groupBy({
        by: ['category'],
        where: { ...whereClause, category: { not: null } },
        _count: true,
        _sum: { customerPrice: true }
      }),
      prisma.order.groupBy({
        by: ['brand'],
        where: { ...whereClause, brand: { not: null } },
        _count: true,
        _sum: { customerPrice: true }
      }),
      prisma.order.groupBy({
        by: ['priority'],
        where: whereClause,
        _count: true
      }),
      prisma.order.groupBy({
        by: ['source'],
        where: whereClause,
        _count: true
      })
    ])

    return {
      totalOrders,
      totalValue: orderAggregates._sum.customerPrice || 0,
      averageOrderValue: orderAggregates._avg.customerPrice || 0,
      categoryBreakdown: categoryStats.map(stat => ({
        category: stat.category || 'Uncategorized',
        count: stat._count,
        value: stat._sum.customerPrice || 0
      })),
      brandBreakdown: brandStats.map(stat => ({
        brand: stat.brand || 'Unknown',
        count: stat._count,
        value: stat._sum.customerPrice || 0
      })),
      priorityBreakdown: priorityStats.map(stat => ({
        priority: stat.priority,
        count: stat._count
      })),
      sourceBreakdown: sourceStats.map(stat => ({
        source: stat.source,
        count: stat._count
      }))
    }
  }

  /**
   * Build where clause from filters (helper method)
   */
  private static buildWhereClause(filters: OrderSearchFilters): any {
    // Implementation similar to searchOrders method
    // This is a simplified version for analytics
    const whereClause: any = {}
    
    if (filters.customerId) whereClause.customerId = filters.customerId
    if (filters.storeCodeId) whereClause.storeCodeId = filters.storeCodeId
    if (filters.createdAfter || filters.createdBefore) {
      whereClause.createdAt = {}
      if (filters.createdAfter) whereClause.createdAt.gte = filters.createdAfter
      if (filters.createdBefore) whereClause.createdAt.lte = filters.createdBefore
    }
    
    return whereClause
  }
}
